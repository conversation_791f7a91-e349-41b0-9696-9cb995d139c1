import React, { useState } from "react";
import { Search, X, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableViewOptions } from "./data-table-view-options";

export function DataTableToolbar({
  table,
  searchColumn = "name",
  searchPlaceholder = "Search...",
  onBulkDelete,
}) {
  const [searchValue, setSearchValue] = useState("");
  const isFiltered = table.getState().columnFilters.length > 0;
  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const hasSelectedRows = selectedRows.length > 0;

  const handleSearch = () => {
    table.getColumn(searchColumn)?.setFilterValue(searchValue);
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  const handleReset = () => {
    setSearchValue("");
    table.resetColumnFilters();
  };

  const handleBulkDelete = () => {
    if (onBulkDelete && hasSelectedRows) {
      const selectedItems = selectedRows.map((row) => row.original);
      onBulkDelete(selectedItems);
    }
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(event) => setSearchValue(event.target.value)}
            onKeyPress={handleKeyPress}
            className="pl-8 max-w-sm"
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleSearch}
          className="h-8"
        >
          <Search className="h-4 w-4" />
        </Button>
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={handleReset}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
        {hasSelectedRows && (
          <Button
            variant="destructive"
            size="sm"
            onClick={handleBulkDelete}
            className="h-8"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete ({selectedRows.length})
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
